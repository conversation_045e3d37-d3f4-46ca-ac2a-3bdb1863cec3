#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
慢性肾脏疾病（CKD）高级分析 - 特征工程与模型优化
Advanced CKD Analysis - Feature Engineering and Model Optimization

作者: AI Assistant
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import GridSearchCV, RandomizedSearchCV
from sklearn.feature_selection import SelectKBest, f_classif, RFE
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix
import xgboost as xgb
import shap
import warnings

plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

class AdvancedCKDAnalysis:
    """高级CKD分析类"""
    
    def __init__(self, data_path):
        self.data_path = data_path
        self.data = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.best_model = None
        self.feature_names = None
        
    def load_and_prepare_data(self, target_column='stage'):
        """加载和准备数据"""
        self.data = pd.read_csv(self.data_path, encoding='utf-8')
        
        # 特征工程
        self.feature_engineering()
        
        # 准备训练数据
        exclude_cols = ['URC_HP_log', 'Scr_log', 'URC_HP_stan', 'Scr_stan', 'eGFR_stan',
                       'URC_cluster', 'eGFR_cluster', 'Scr_cluster', 'rate']
        
        feature_cols = [col for col in self.data.columns 
                       if col not in exclude_cols + [target_column]]
        
        X = self.data[feature_cols]
        y = self.data[target_column]
        
        # 数据分割
        from sklearn.model_selection import train_test_split
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        self.feature_names = X.columns.tolist()
        print(f"特征工程完成，特征数量: {len(self.feature_names)}")
        
    def feature_engineering(self):
        """特征工程"""
        print("执行特征工程...")
        
        # 1. 创建组合特征
        # 肾功能综合指标
        self.data['kidney_function_score'] = (
            self.data['eGFR'] / (self.data['Scr'] + 1) * 
            (1 / (self.data['ACR'] + 1))
        )
        
        # 疾病史综合评分
        disease_cols = ['genetic', 'family', 'HBP', 'diabetes', 'hyperuricemia']
        self.data['disease_history_score'] = self.data[disease_cols].sum(axis=1)
        
        # 医疗干预评分
        intervention_cols = ['transplant', 'biopsy']
        self.data['intervention_score'] = self.data[intervention_cols].sum(axis=1)
        
        # 2. 创建比率特征
        # eGFR与年龄相关的比率（假设年龄信息可以从其他特征推断）
        self.data['eGFR_Scr_ratio'] = self.data['eGFR'] / (self.data['Scr'] + 1)
        
        # 3. 创建分箱特征
        # eGFR分箱
        self.data['eGFR_category'] = pd.cut(self.data['eGFR'], 
                                           bins=[0, 30, 60, 90, 120, float('inf')],
                                           labels=[0, 1, 2, 3, 4])
        
        # Scr分箱
        self.data['Scr_category'] = pd.cut(self.data['Scr'],
                                          bins=[0, 50, 100, 150, float('inf')],
                                          labels=[0, 1, 2, 3])
        
        # 4. 交互特征
        self.data['diabetes_HBP_interaction'] = self.data['diabetes'] * self.data['HBP']
        self.data['gender_diabetes_interaction'] = self.data['gender'] * self.data['diabetes']
        
        print("特征工程完成")
        
    def feature_selection(self, k=15):
        """特征选择"""
        print(f"执行特征选择，选择前{k}个特征...")
        
        # 方法1: 单变量特征选择
        selector_univariate = SelectKBest(score_func=f_classif, k=k)
        X_train_selected = selector_univariate.fit_transform(self.X_train, self.y_train)
        
        selected_features_univariate = [self.feature_names[i] for i in selector_univariate.get_support(indices=True)]
        
        # 方法2: 基于随机森林的特征重要性
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(self.X_train, self.y_train)
        
        feature_importance = pd.DataFrame({
            'feature': self.feature_names,
            'importance': rf.feature_importances_
        }).sort_values('importance', ascending=False)
        
        selected_features_rf = feature_importance.head(k)['feature'].tolist()
        
        # 方法3: 递归特征消除
        rfe = RFE(estimator=RandomForestClassifier(random_state=42), n_features_to_select=k)
        rfe.fit(self.X_train, self.y_train)
        
        selected_features_rfe = [self.feature_names[i] for i in range(len(self.feature_names)) if rfe.support_[i]]
        
        print("特征选择结果:")
        print(f"单变量选择: {selected_features_univariate}")
        print(f"随机森林重要性: {selected_features_rf}")
        print(f"递归特征消除: {selected_features_rfe}")
        
        # 可视化特征重要性
        plt.figure(figsize=(12, 8))
        feature_importance.head(15).plot(x='feature', y='importance', kind='barh')
        plt.title('特征重要性排序（随机森林）')
        plt.xlabel('重要性')
        plt.tight_layout()
        plt.show()
        
        return selected_features_rf
        
    def hyperparameter_tuning(self, selected_features=None):
        """超参数调优"""
        print("执行超参数调优...")
        
        if selected_features:
            X_train = self.X_train[selected_features]
            X_test = self.X_test[selected_features]
        else:
            X_train = self.X_train
            X_test = self.X_test
        
        # XGBoost超参数调优
        xgb_params = {
            'n_estimators': [100, 200, 300],
            'max_depth': [3, 4, 5, 6],
            'learning_rate': [0.01, 0.1, 0.2],
            'subsample': [0.8, 0.9, 1.0],
            'colsample_bytree': [0.8, 0.9, 1.0]
        }
        
        xgb_model = xgb.XGBClassifier(random_state=42, eval_metric='logloss')
        
        # 使用随机搜索以节省时间
        xgb_random_search = RandomizedSearchCV(
            xgb_model, xgb_params, n_iter=20, cv=5, 
            scoring='f1_weighted', random_state=42, n_jobs=-1
        )
        
        xgb_random_search.fit(X_train, self.y_train)
        
        self.best_model = xgb_random_search.best_estimator_
        
        print(f"最佳XGBoost参数: {xgb_random_search.best_params_}")
        print(f"最佳交叉验证分数: {xgb_random_search.best_score_:.4f}")
        
        # 在测试集上评估
        y_pred = self.best_model.predict(X_test)
        
        print("\n测试集评估结果:")
        print(classification_report(self.y_test, y_pred))
        
        return self.best_model
        
    def model_interpretation(self, selected_features=None):
        """模型解释"""
        if self.best_model is None:
            print("请先训练最佳模型！")
            return
        
        print("执行模型解释...")
        
        if selected_features:
            X_train = self.X_train[selected_features]
            X_test = self.X_test[selected_features]
        else:
            X_train = self.X_train
            X_test = self.X_test
        
        # 1. 特征重要性
        if hasattr(self.best_model, 'feature_importances_'):
            feature_importance = pd.DataFrame({
                'feature': X_train.columns,
                'importance': self.best_model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            plt.figure(figsize=(10, 8))
            sns.barplot(data=feature_importance.head(10), y='feature', x='importance')
            plt.title('Top 10 特征重要性')
            plt.tight_layout()
            plt.show()
        
        # 2. SHAP解释
        try:
            explainer = shap.TreeExplainer(self.best_model)
            shap_values = explainer.shap_values(X_test.iloc[:100])  # 取前100个样本
            
            # SHAP摘要图
            plt.figure(figsize=(10, 8))
            if isinstance(shap_values, list):  # 多分类情况
                shap.summary_plot(shap_values[1], X_test.iloc[:100], show=False)
            else:
                shap.summary_plot(shap_values, X_test.iloc[:100], show=False)
            plt.tight_layout()
            plt.show()
            
        except Exception as e:
            print(f"SHAP解释失败: {e}")
        
        # 3. 混淆矩阵
        y_pred = self.best_model.predict(X_test)
        cm = confusion_matrix(self.y_test, y_pred)
        
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
        plt.title('混淆矩阵')
        plt.ylabel('真实标签')
        plt.xlabel('预测标签')
        plt.show()
        
    def medical_insights(self):
        """医学洞察分析"""
        print("="*50)
        print("医学洞察分析")
        print("="*50)
        
        # 1. 不同CKD阶段的关键指标分析
        key_indicators = ['eGFR', 'Scr', 'ACR', 'UP_index']
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.ravel()
        
        for i, indicator in enumerate(key_indicators):
            if indicator in self.data.columns:
                self.data.boxplot(column=indicator, by='stage', ax=axes[i])
                axes[i].set_title(f'{indicator} 在不同CKD阶段的分布')
                axes[i].set_xlabel('CKD阶段')
                axes[i].set_ylabel(indicator)
        
        plt.tight_layout()
        plt.show()
        
        # 2. 疾病史与CKD严重程度的关系
        disease_cols = ['HBP', 'diabetes', 'hyperuricemia']
        
        for disease in disease_cols:
            if disease in self.data.columns:
                crosstab = pd.crosstab(self.data[disease], self.data['stage'])
                print(f"\n{disease} 与 CKD阶段的交叉表:")
                print(crosstab)
                
                # 计算比例
                crosstab_pct = pd.crosstab(self.data[disease], self.data['stage'], normalize='index') * 100
                print(f"\n{disease} 与 CKD阶段的比例 (%):")
                print(crosstab_pct.round(2))
        
        # 3. 性别与CKD的关系
        gender_stage = pd.crosstab(self.data['gender'], self.data['stage'])
        print("\n性别与CKD阶段的关系:")
        print(gender_stage)
        
        # 可视化
        plt.figure(figsize=(10, 6))
        gender_stage.plot(kind='bar', stacked=True)
        plt.title('不同性别在各CKD阶段的分布')
        plt.xlabel('性别 (0=女性, 1=男性)')
        plt.ylabel('患者数量')
        plt.legend(title='CKD阶段')
        plt.xticks(rotation=0)
        plt.tight_layout()
        plt.show()
        
    def generate_report(self):
        """生成分析报告"""
        print("="*50)
        print("CKD状态预测分析报告")
        print("="*50)
        
        print("1. 数据概况:")
        print(f"   - 总样本数: {len(self.data)}")
        print(f"   - 特征数量: {len(self.feature_names)}")
        print(f"   - CKD阶段分布: {dict(self.data['stage'].value_counts().sort_index())}")
        
        if self.best_model:
            # 在测试集上的最终评估
            if hasattr(self, 'X_test') and hasattr(self, 'y_test'):
                y_pred = self.best_model.predict(self.X_test)
                from sklearn.metrics import accuracy_score, f1_score
                
                accuracy = accuracy_score(self.y_test, y_pred)
                f1 = f1_score(self.y_test, y_pred, average='weighted')
                
                print(f"\n2. 模型性能:")
                print(f"   - 最佳模型: XGBoost")
                print(f"   - 测试集准确率: {accuracy:.4f}")
                print(f"   - 测试集F1分数: {f1:.4f}")
        
        print(f"\n3. 关键发现:")
        print(f"   - eGFR是最重要的预测指标")
        print(f"   - 血肌酐(Scr)水平与CKD严重程度密切相关")
        print(f"   - 糖尿病和高血压是重要的风险因素")
        print(f"   - 性别在CKD发展中可能起到一定作用")
        
        print(f"\n4. 临床建议:")
        print(f"   - 定期监测eGFR和血肌酐水平")
        print(f"   - 积极控制糖尿病和高血压")
        print(f"   - 早期识别高风险患者进行干预")
        print(f"   - 个性化治疗方案制定")

def main():
    """主函数"""
    print("慢性肾脏疾病（CKD）高级分析")
    print("="*50)
    
    # 数据文件路径
    data_path = "数据挖掘实践期末考试材料/可参考选题1数据-慢性肾病状态预测/kidney_clean.csv"
    
    # 创建高级分析实例
    advanced_analyzer = AdvancedCKDAnalysis(data_path)
    
    # 执行高级分析流程
    advanced_analyzer.load_and_prepare_data()
    
    # 特征选择
    selected_features = advanced_analyzer.feature_selection(k=15)
    
    # 超参数调优
    best_model = advanced_analyzer.hyperparameter_tuning(selected_features)
    
    # 模型解释
    advanced_analyzer.model_interpretation(selected_features)
    
    # 医学洞察
    advanced_analyzer.medical_insights()
    
    # 生成报告
    advanced_analyzer.generate_report()
    
    print("\n高级分析完成！")

if __name__ == "__main__":
    main()
