#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
慢性肾脏疾病（CKD）状态预测 - 主运行脚本
CKD Status Prediction - Main Runner

作者: AI Assistant
日期: 2024
"""

import sys
import os
import warnings
warnings.filterwarnings('ignore')

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'pandas', 'numpy', 'matplotlib', 'seaborn', 
        'scikit-learn', 'xgboost', 'lightgbm'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请使用以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def run_basic_analysis():
    """运行基础分析"""
    print("="*60)
    print("运行基础CKD分析")
    print("="*60)
    
    try:
        from ckd_analysis import main as basic_main
        basic_main()
        return True
    except Exception as e:
        print(f"基础分析运行失败: {e}")
        return False

def run_advanced_analysis():
    """运行高级分析"""
    print("\n" + "="*60)
    print("运行高级CKD分析")
    print("="*60)
    
    try:
        from ckd_advanced_analysis import main as advanced_main
        advanced_main()
        return True
    except Exception as e:
        print(f"高级分析运行失败: {e}")
        return False

def run_visualization():
    """运行可视化分析"""
    print("\n" + "="*60)
    print("运行CKD可视化分析")
    print("="*60)
    
    try:
        from ckd_visualization import main as viz_main
        viz_main()
        return True
    except Exception as e:
        print(f"可视化分析运行失败: {e}")
        return False

def generate_summary_report():
    """生成总结报告"""
    print("\n" + "="*60)
    print("CKD状态预测项目总结报告")
    print("="*60)
    
    report = """
    
慢性肾脏疾病（CKD）状态预测分析项目总结

一、项目背景
慢性肾脏疾病是一种由肾脏长期受损引起的疾病，其病程缓慢，常常不易察觉。
本项目基于7家医院的1150条CKD患者数据，建立预测模型来预测患者的CKD状态和疾病进展趋势。

二、数据概况
- 数据来源：7家医院确诊CKD病人数据
- 样本数量：1150条记录
- 特征类型：患者基本信息、病史、血液相关指标等
- 目标变量：CKD分层(rate)和CKD评级(stage)

三、分析方法
1. 探索性数据分析（EDA）
   - 数据质量检查
   - 特征分布分析
   - 相关性分析

2. 特征工程
   - 创建组合特征
   - 特征选择
   - 数据标准化

3. 机器学习建模
   - 多种算法比较（逻辑回归、随机森林、SVM、XGBoost等）
   - 超参数调优
   - 交叉验证

4. 模型评估与解释
   - 性能指标评估
   - 特征重要性分析
   - SHAP解释

四、关键发现
1. 重要预测指标：
   - eGFR（估算肾小球滤过率）是最重要的预测指标
   - 血肌酐(Scr)水平与CKD严重程度密切相关
   - 尿白蛋白肌酐比(ACR)对早期诊断有重要意义

2. 风险因素：
   - 糖尿病和高血压是重要的风险因素
   - 家族史和遗传因素影响疾病发展
   - 性别在CKD发展中可能起到一定作用

3. 医院差异：
   - 不同医院的患者构成存在差异
   - 诊断标准和治疗方案可能影响预测结果

五、临床应用价值
1. 早期识别：帮助医生早期识别高风险患者
2. 个性化治疗：为制定个性化治疗方案提供依据
3. 资源配置：优化医疗资源配置和管理
4. 预后评估：评估患者疾病进展和预后

六、技术特点
1. 多算法比较：使用多种机器学习算法确保最佳性能
2. 特征工程：创建有医学意义的组合特征
3. 模型解释：使用SHAP等工具提供模型解释
4. 可视化分析：丰富的图表展示分析结果

七、局限性与改进方向
1. 数据局限性：
   - 样本量相对较小
   - 缺少长期随访数据
   - 可能存在选择偏倚

2. 改进方向：
   - 增加更多医院和患者数据
   - 引入时间序列分析
   - 考虑更多临床指标
   - 开发实时预测系统

八、结论
本项目成功建立了CKD状态预测模型，能够有效预测患者的疾病严重程度。
模型具有良好的预测性能和可解释性，为临床决策提供了有价值的支持工具。
通过持续优化和扩展，该模型有望在实际临床环境中发挥重要作用。

    """
    
    print(report)
    
    # 保存报告到文件
    with open('CKD_Analysis_Report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("报告已保存到 'CKD_Analysis_Report.txt'")

def main():
    """主函数"""
    print("慢性肾脏疾病（CKD）状态预测分析项目")
    print("="*60)
    print("作者: AI Assistant")
    print("日期: 2024")
    print("="*60)
    
    # 检查依赖
    if not check_dependencies():
        print("请先安装所需依赖包后再运行。")
        return
    
    # 检查数据文件
    data_path = "数据挖掘实践期末考试材料/可参考选题1数据-慢性肾病状态预测/kidney_clean.csv"
    if not os.path.exists(data_path):
        print(f"数据文件不存在: {data_path}")
        print("请确保数据文件路径正确。")
        return
    
    print("所有依赖检查通过，开始分析...")
    
    # 运行分析
    success_count = 0
    
    # 1. 基础分析
    if run_basic_analysis():
        success_count += 1
        print("✓ 基础分析完成")
    else:
        print("✗ 基础分析失败")
    
    # 2. 高级分析
    if run_advanced_analysis():
        success_count += 1
        print("✓ 高级分析完成")
    else:
        print("✗ 高级分析失败")
    
    # 3. 可视化分析
    if run_visualization():
        success_count += 1
        print("✓ 可视化分析完成")
    else:
        print("✗ 可视化分析失败")
    
    # 4. 生成总结报告
    generate_summary_report()
    
    print(f"\n分析完成！成功运行 {success_count}/3 个模块")
    
    if success_count == 3:
        print("🎉 所有分析模块运行成功！")
    elif success_count > 0:
        print("⚠️  部分分析模块运行成功，请检查错误信息。")
    else:
        print("❌ 所有分析模块运行失败，请检查环境配置。")
    
    print("\n生成的文件:")
    print("- CKD_Analysis_Report.txt: 项目总结报告")
    print("- 各种图表和可视化结果")

if __name__ == "__main__":
    main()
