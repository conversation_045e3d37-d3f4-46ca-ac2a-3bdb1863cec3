# 慢性肾脏疾病（CKD）状态预测分析项目

## 项目概述

本项目基于7家医院的1150条慢性肾脏疾病（CKD）患者数据，使用机器学习方法建立预测模型，用于预测患者的CKD状态和疾病进展趋势。项目旨在为医生提供可靠的决策支持，帮助早期识别高风险患者并制定个性化治疗方案。

## 项目背景

慢性肾脏疾病（Chronic Kidney Disease, CKD）是一种由肾脏长期受损引起的疾病，其病程缓慢，常常不易察觉。CKD分为5个阶段：

- **CKD1、2期**: 早期阶段，通常没有明显症状
- **CKD3期**: 肾脏已受损，可能出现水肿、疲劳、高血压等症状
- **CKD4期**: 肾功能进一步下降，出现骨质疏松、食欲不振等症状
- **CKD5期**: 终末期肾病，症状明显和严重

## 数据说明

### 数据来源
- **数据集**: 7家医院确诊CKD病人数据
- **样本数量**: 1150条记录
- **数据文件**: `kidney_clean.csv`

### 主要特征
| 特征名 | 含义 | 取值范围 |
|--------|------|----------|
| hos_name | 医院名称 | 7所医院 |
| gender | 性别 | 男/女 |
| genetic | 遗传性肾脏病史 | 有/无 |
| family | 慢性肾炎家族史 | 有/无 |
| HBP | 高血压病史 | 有/无 |
| diabetes | 糖尿病病史 | 有/无 |
| eGFR | 估算肾小球滤过率 | 2.5-148 |
| Scr | 血肌酐 | 27.2-85800 |
| ACR | 尿白蛋白肌酐比 | <30/30-300/>300 |
| stage | CKD评级 | CKD 1-5期 |
| rate | CKD分层 | 低危/中危/高危/极高危 |

## 项目结构

```
├── ckd_analysis.py              # 基础分析模块
├── ckd_advanced_analysis.py     # 高级分析模块（特征工程+模型优化）
├── ckd_visualization.py         # 可视化分析模块
├── run_ckd_analysis.py          # 主运行脚本
├── README.md                    # 项目说明文档
└── requirements.txt             # 依赖包列表
```

## 安装依赖

```bash
pip install pandas numpy matplotlib seaborn scikit-learn xgboost lightgbm plotly shap
```

或者使用requirements.txt：

```bash
pip install -r requirements.txt
```

## 使用方法

### 快速开始

运行主脚本执行完整分析：

```bash
python run_ckd_analysis.py
```

### 分模块运行

1. **基础分析**：
```bash
python ckd_analysis.py
```

2. **高级分析**：
```bash
python ckd_advanced_analysis.py
```

3. **可视化分析**：
```bash
python ckd_visualization.py
```

## 分析模块详解

### 1. 基础分析模块 (`ckd_analysis.py`)

- **数据概览**: 基本统计信息、缺失值检查
- **目标变量分析**: CKD阶段和分层分布
- **特征分析**: 分类特征和数值特征统计
- **相关性分析**: 特征间相关性和与目标变量的关系
- **模型训练**: 多种机器学习算法比较
- **模型评估**: 性能指标计算和比较

### 2. 高级分析模块 (`ckd_advanced_analysis.py`)

- **特征工程**: 
  - 组合特征创建
  - 比率特征
  - 分箱特征
  - 交互特征
- **特征选择**: 
  - 单变量选择
  - 基于重要性的选择
  - 递归特征消除
- **超参数调优**: 网格搜索和随机搜索
- **模型解释**: SHAP值分析
- **医学洞察**: 临床意义分析

### 3. 可视化分析模块 (`ckd_visualization.py`)

- **基础分布图**: 饼图、条形图
- **临床指标分析**: 箱线图、散点图
- **风险因素分析**: 堆叠条形图、交叉表
- **相关性热力图**: 特征相关性可视化
- **交互式图表**: 3D散点图、平行坐标图
- **医学洞察可视化**: 小提琴图、密度图

## 主要功能

### 数据分析
- ✅ 数据质量检查和清洗
- ✅ 探索性数据分析（EDA）
- ✅ 特征分布和相关性分析
- ✅ 医学指标统计分析

### 机器学习
- ✅ 多算法模型比较
- ✅ 特征工程和选择
- ✅ 超参数调优
- ✅ 交叉验证
- ✅ 模型性能评估

### 可视化
- ✅ 静态图表（matplotlib, seaborn）
- ✅ 交互式图表（plotly）
- ✅ 医学专业图表
- ✅ 模型解释可视化

### 模型解释
- ✅ 特征重要性分析
- ✅ SHAP值解释
- ✅ 混淆矩阵分析
- ✅ 医学意义解释

## 技术栈

- **数据处理**: pandas, numpy
- **机器学习**: scikit-learn, xgboost, lightgbm
- **可视化**: matplotlib, seaborn, plotly
- **模型解释**: shap
- **统计分析**: scipy

## 评估指标

- **准确率 (Accuracy)**: 整体预测正确率
- **精确率 (Precision)**: 正例预测的准确性
- **召回率 (Recall)**: 正例的覆盖率
- **F1分数**: 精确率和召回率的调和平均
- **AUC**: ROC曲线下面积
- **交叉验证分数**: 模型稳定性评估

## 主要发现

### 重要预测指标
1. **eGFR（估算肾小球滤过率）**: 最重要的预测指标
2. **血肌酐(Scr)**: 与CKD严重程度密切相关
3. **尿白蛋白肌酐比(ACR)**: 对早期诊断有重要意义

### 风险因素
1. **糖尿病和高血压**: 重要的风险因素
2. **家族史和遗传因素**: 影响疾病发展
3. **性别差异**: 在CKD发展中可能起作用

### 临床应用价值
1. **早期识别**: 帮助医生早期识别高风险患者
2. **个性化治疗**: 为制定治疗方案提供依据
3. **资源配置**: 优化医疗资源配置
4. **预后评估**: 评估疾病进展和预后

## 输出文件

运行完成后会生成以下文件：
- `CKD_Analysis_Report.txt`: 详细分析报告
- 各种图表文件（PNG格式）
- 模型性能评估结果

## 注意事项

1. **数据路径**: 确保数据文件路径正确
2. **依赖安装**: 运行前请安装所有必需的依赖包
3. **内存要求**: 建议至少4GB内存
4. **Python版本**: 建议使用Python 3.7+

## 贡献指南

欢迎提交Issue和Pull Request来改进项目：

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目仅用于学术研究和教育目的。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至项目维护者

---

**免责声明**: 本项目仅用于研究和教育目的，不应用于实际临床诊断。任何医疗决策都应咨询专业医生。
