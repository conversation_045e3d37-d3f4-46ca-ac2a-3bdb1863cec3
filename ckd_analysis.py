#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
慢性肾脏疾病（CKD）状态预测分析
Chronic Kidney Disease (CKD) Status Prediction Analysis

作者: AI Assistant
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import xgboost as xgb
import lightgbm as lgb

# 设置中文字体和图形样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
warnings.filterwarnings('ignore')

class CKDAnalysis:
    """慢性肾脏疾病分析类"""
    
    def __init__(self, data_path):
        """
        初始化分析类
        
        Parameters:
        data_path (str): 数据文件路径
        """
        self.data_path = data_path
        self.data = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.models = {}
        self.results = {}
        
    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv(self.data_path, encoding='utf-8')
            print(f"数据加载成功！数据形状: {self.data.shape}")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def data_overview(self):
        """数据概览"""
        if self.data is None:
            print("请先加载数据！")
            return
        
        print("="*50)
        print("数据基本信息")
        print("="*50)
        print(f"数据形状: {self.data.shape}")
        print(f"特征数量: {self.data.shape[1]}")
        print(f"样本数量: {self.data.shape[0]}")
        
        print("\n数据类型:")
        print(self.data.dtypes)
        
        print("\n缺失值统计:")
        missing_data = self.data.isnull().sum()
        if missing_data.sum() > 0:
            print(missing_data[missing_data > 0])
        else:
            print("无缺失值")
        
        print("\n数据前5行:")
        print(self.data.head())
        
        print("\n数值型特征统计:")
        print(self.data.describe())
        
    def target_analysis(self):
        """目标变量分析"""
        if self.data is None:
            print("请先加载数据！")
            return
        
        print("="*50)
        print("目标变量分析")
        print("="*50)
        
        # CKD分层(rate)分析
        print("CKD分层(rate)分布:")
        rate_counts = self.data['rate'].value_counts().sort_index()
        print(rate_counts)
        
        # CKD评级(stage)分析
        print("\nCKD评级(stage)分布:")
        stage_counts = self.data['stage'].value_counts().sort_index()
        print(stage_counts)
        
        # 可视化目标变量分布
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # CKD分层分布
        rate_counts.plot(kind='bar', ax=axes[0], color='skyblue')
        axes[0].set_title('CKD分层(rate)分布')
        axes[0].set_xlabel('CKD分层')
        axes[0].set_ylabel('患者数量')
        axes[0].tick_params(axis='x', rotation=0)
        
        # CKD评级分布
        stage_counts.plot(kind='bar', ax=axes[1], color='lightcoral')
        axes[1].set_title('CKD评级(stage)分布')
        axes[1].set_xlabel('CKD评级')
        axes[1].set_ylabel('患者数量')
        axes[1].tick_params(axis='x', rotation=0)
        
        plt.tight_layout()
        plt.show()
        
    def feature_analysis(self):
        """特征分析"""
        if self.data is None:
            print("请先加载数据！")
            return
        
        print("="*50)
        print("特征分析")
        print("="*50)
        
        # 分类特征分析
        categorical_features = ['hos_name', 'gender', 'genetic', 'family', 'transplant', 
                              'biopsy', 'HBP', 'diabetes', 'hyperuricemia', 'UAS']
        
        print("分类特征分布:")
        for feature in categorical_features:
            if feature in self.data.columns:
                print(f"\n{feature}:")
                print(self.data[feature].value_counts())
        
        # 数值特征分析
        numerical_features = ['UP_index', 'URC_HP', 'ACR', 'Scr', 'eGFR']
        
        print("\n数值特征统计:")
        for feature in numerical_features:
            if feature in self.data.columns:
                print(f"\n{feature}:")
                print(f"均值: {self.data[feature].mean():.2f}")
                print(f"标准差: {self.data[feature].std():.2f}")
                print(f"最小值: {self.data[feature].min():.2f}")
                print(f"最大值: {self.data[feature].max():.2f}")
        
    def correlation_analysis(self):
        """相关性分析"""
        if self.data is None:
            print("请先加载数据！")
            return
        
        # 选择数值型特征进行相关性分析
        numerical_cols = self.data.select_dtypes(include=[np.number]).columns
        correlation_matrix = self.data[numerical_cols].corr()
        
        # 绘制相关性热力图
        plt.figure(figsize=(12, 10))
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5)
        plt.title('特征相关性热力图')
        plt.tight_layout()
        plt.show()
        
        # 与目标变量的相关性
        print("与CKD分层(rate)的相关性:")
        rate_corr = correlation_matrix['rate'].sort_values(ascending=False)
        print(rate_corr)
        
        print("\n与CKD评级(stage)的相关性:")
        stage_corr = correlation_matrix['stage'].sort_values(ascending=False)
        print(stage_corr)
        
    def data_preprocessing(self, target_column='stage'):
        """数据预处理"""
        if self.data is None:
            print("请先加载数据！")
            return False
        
        print("="*50)
        print("数据预处理")
        print("="*50)
        
        # 复制数据
        processed_data = self.data.copy()
        
        # 选择特征（排除一些不需要的列）
        exclude_cols = ['URC_HP_log', 'Scr_log', 'URC_HP_stan', 'Scr_stan', 'eGFR_stan',
                       'URC_cluster', 'eGFR_cluster', 'Scr_cluster']
        
        feature_cols = [col for col in processed_data.columns 
                       if col not in exclude_cols + [target_column]]
        
        X = processed_data[feature_cols]
        y = processed_data[target_column]
        
        print(f"特征数量: {X.shape[1]}")
        print(f"样本数量: {X.shape[0]}")
        print(f"目标变量: {target_column}")
        print(f"目标变量类别: {sorted(y.unique())}")
        
        # 数据分割
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        print(f"训练集大小: {self.X_train.shape}")
        print(f"测试集大小: {self.X_test.shape}")
        
        return True
        
    def train_models(self):
        """训练多个机器学习模型"""
        if self.X_train is None:
            print("请先进行数据预处理！")
            return
        
        print("="*50)
        print("模型训练")
        print("="*50)
        
        # 定义模型
        models = {
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
            'Random Forest': RandomForestClassifier(random_state=42, n_estimators=100),
            'SVM': SVC(random_state=42, probability=True),
            'Gradient Boosting': GradientBoostingClassifier(random_state=42),
            'XGBoost': xgb.XGBClassifier(random_state=42, eval_metric='logloss'),
            'LightGBM': lgb.LGBMClassifier(random_state=42, verbose=-1)
        }
        
        # 训练和评估每个模型
        for name, model in models.items():
            print(f"\n训练 {name}...")
            
            try:
                # 训练模型
                model.fit(self.X_train, self.y_train)
                
                # 预测
                y_pred = model.predict(self.X_test)
                y_pred_proba = model.predict_proba(self.X_test)
                
                # 计算评估指标
                accuracy = accuracy_score(self.y_test, y_pred)
                precision = precision_score(self.y_test, y_pred, average='weighted')
                recall = recall_score(self.y_test, y_pred, average='weighted')
                f1 = f1_score(self.y_test, y_pred, average='weighted')
                
                # 计算AUC（多分类）
                try:
                    auc = roc_auc_score(self.y_test, y_pred_proba, multi_class='ovr')
                except:
                    auc = 0.0
                
                # 交叉验证
                cv_scores = cross_val_score(model, self.X_train, self.y_train, cv=5)
                
                # 保存结果
                self.models[name] = model
                self.results[name] = {
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1,
                    'auc': auc,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std()
                }
                
                print(f"{name} 训练完成")
                print(f"准确率: {accuracy:.4f}")
                print(f"F1分数: {f1:.4f}")
                print(f"交叉验证均值: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
                
            except Exception as e:
                print(f"{name} 训练失败: {e}")
        
    def evaluate_models(self):
        """模型评估和比较"""
        if not self.results:
            print("请先训练模型！")
            return
        
        print("="*50)
        print("模型评估结果")
        print("="*50)
        
        # 创建结果DataFrame
        results_df = pd.DataFrame(self.results).T
        results_df = results_df.round(4)
        
        print(results_df)
        
        # 可视化模型比较
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 准确率比较
        results_df['accuracy'].plot(kind='bar', ax=axes[0,0], color='skyblue')
        axes[0,0].set_title('模型准确率比较')
        axes[0,0].set_ylabel('准确率')
        axes[0,0].tick_params(axis='x', rotation=45)
        
        # F1分数比较
        results_df['f1_score'].plot(kind='bar', ax=axes[0,1], color='lightcoral')
        axes[0,1].set_title('模型F1分数比较')
        axes[0,1].set_ylabel('F1分数')
        axes[0,1].tick_params(axis='x', rotation=45)
        
        # AUC比较
        results_df['auc'].plot(kind='bar', ax=axes[1,0], color='lightgreen')
        axes[1,0].set_title('模型AUC比较')
        axes[1,0].set_ylabel('AUC')
        axes[1,0].tick_params(axis='x', rotation=45)
        
        # 交叉验证分数比较
        results_df['cv_mean'].plot(kind='bar', ax=axes[1,1], color='orange')
        axes[1,1].set_title('交叉验证分数比较')
        axes[1,1].set_ylabel('CV分数')
        axes[1,1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.show()
        
        # 找出最佳模型
        best_model_name = results_df['f1_score'].idxmax()
        print(f"\n最佳模型: {best_model_name}")
        print(f"F1分数: {results_df.loc[best_model_name, 'f1_score']:.4f}")
        
        return best_model_name

def main():
    """主函数"""
    print("慢性肾脏疾病（CKD）状态预测分析")
    print("="*50)
    
    # 数据文件路径
    data_path = "数据挖掘实践期末考试材料/可参考选题1数据-慢性肾病状态预测/kidney_clean.csv"
    
    # 创建分析实例
    ckd_analyzer = CKDAnalysis(data_path)
    
    # 执行分析流程
    if ckd_analyzer.load_data():
        ckd_analyzer.data_overview()
        ckd_analyzer.target_analysis()
        ckd_analyzer.feature_analysis()
        ckd_analyzer.correlation_analysis()
        
        if ckd_analyzer.data_preprocessing():
            ckd_analyzer.train_models()
            best_model = ckd_analyzer.evaluate_models()
            
            print(f"\n分析完成！最佳模型: {best_model}")
    
if __name__ == "__main__":
    main()
