#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
慢性肾脏疾病（CKD）可视化分析
CKD Visualization Analysis

作者: AI Assistant
日期: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings

plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
warnings.filterwarnings('ignore')

class CKDVisualization:
    """CKD可视化分析类"""
    
    def __init__(self, data_path):
        self.data_path = data_path
        self.data = None
        
    def load_data(self):
        """加载数据"""
        self.data = pd.read_csv(self.data_path, encoding='utf-8')
        print(f"数据加载成功！数据形状: {self.data.shape}")
        
    def basic_distribution_plots(self):
        """基础分布图"""
        print("生成基础分布图...")
        
        # 1. CKD阶段和分层分布
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # CKD阶段分布
        stage_counts = self.data['stage'].value_counts().sort_index()
        axes[0,0].pie(stage_counts.values, labels=[f'Stage {i}' for i in stage_counts.index], 
                     autopct='%1.1f%%', startangle=90)
        axes[0,0].set_title('CKD阶段分布')
        
        # CKD分层分布
        rate_counts = self.data['rate'].value_counts().sort_index()
        rate_labels = ['低危', '中危', '高危', '极高危']
        axes[0,1].pie(rate_counts.values, labels=rate_labels[:len(rate_counts)], 
                     autopct='%1.1f%%', startangle=90)
        axes[0,1].set_title('CKD分层分布')
        
        # 性别分布
        gender_counts = self.data['gender'].value_counts()
        axes[1,0].bar(['女性', '男性'], gender_counts.values, color=['pink', 'lightblue'])
        axes[1,0].set_title('性别分布')
        axes[1,0].set_ylabel('患者数量')
        
        # 医院分布
        hospital_counts = self.data['hos_name'].value_counts()
        axes[1,1].bar(range(len(hospital_counts)), hospital_counts.values, color='lightgreen')
        axes[1,1].set_title('医院分布')
        axes[1,1].set_xlabel('医院编号')
        axes[1,1].set_ylabel('患者数量')
        axes[1,1].set_xticks(range(len(hospital_counts)))
        axes[1,1].set_xticklabels([f'医院{i}' for i in hospital_counts.index])
        
        plt.tight_layout()
        plt.show()
        
    def clinical_indicators_analysis(self):
        """临床指标分析"""
        print("生成临床指标分析图...")
        
        # 关键临床指标
        key_indicators = ['eGFR', 'Scr', 'ACR', 'UP_index', 'URC_HP']
        
        # 1. 不同CKD阶段的指标分布
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.ravel()
        
        for i, indicator in enumerate(key_indicators):
            if indicator in self.data.columns and i < len(axes):
                # 箱线图
                self.data.boxplot(column=indicator, by='stage', ax=axes[i])
                axes[i].set_title(f'{indicator} 在不同CKD阶段的分布')
                axes[i].set_xlabel('CKD阶段')
                axes[i].set_ylabel(indicator)
        
        # 删除多余的子图
        if len(key_indicators) < len(axes):
            for j in range(len(key_indicators), len(axes)):
                fig.delaxes(axes[j])
        
        plt.tight_layout()
        plt.show()
        
        # 2. 关键指标的相关性散点图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # eGFR vs Scr
        scatter = axes[0,0].scatter(self.data['Scr'], self.data['eGFR'], 
                                   c=self.data['stage'], cmap='viridis', alpha=0.6)
        axes[0,0].set_xlabel('血肌酐 (Scr)')
        axes[0,0].set_ylabel('估算肾小球滤过率 (eGFR)')
        axes[0,0].set_title('eGFR vs 血肌酐')
        plt.colorbar(scatter, ax=axes[0,0], label='CKD阶段')
        
        # eGFR vs ACR
        scatter = axes[0,1].scatter(self.data['ACR'], self.data['eGFR'], 
                                   c=self.data['stage'], cmap='viridis', alpha=0.6)
        axes[0,1].set_xlabel('尿白蛋白肌酐比 (ACR)')
        axes[0,1].set_ylabel('估算肾小球滤过率 (eGFR)')
        axes[0,1].set_title('eGFR vs ACR')
        plt.colorbar(scatter, ax=axes[0,1], label='CKD阶段')
        
        # Scr vs ACR
        scatter = axes[1,0].scatter(self.data['ACR'], self.data['Scr'], 
                                   c=self.data['stage'], cmap='viridis', alpha=0.6)
        axes[1,0].set_xlabel('尿白蛋白肌酐比 (ACR)')
        axes[1,0].set_ylabel('血肌酐 (Scr)')
        axes[1,0].set_title('血肌酐 vs ACR')
        plt.colorbar(scatter, ax=axes[1,0], label='CKD阶段')
        
        # UP_index vs stage
        up_stage = self.data.groupby(['UP_index', 'stage']).size().unstack(fill_value=0)
        up_stage.plot(kind='bar', stacked=True, ax=axes[1,1])
        axes[1,1].set_title('尿蛋白指标与CKD阶段关系')
        axes[1,1].set_xlabel('尿蛋白指标')
        axes[1,1].set_ylabel('患者数量')
        axes[1,1].legend(title='CKD阶段')
        axes[1,1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.show()
        
    def risk_factors_analysis(self):
        """风险因素分析"""
        print("生成风险因素分析图...")
        
        # 疾病史相关因素
        risk_factors = ['HBP', 'diabetes', 'hyperuricemia', 'genetic', 'family']
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.ravel()
        
        for i, factor in enumerate(risk_factors):
            if factor in self.data.columns and i < len(axes):
                # 交叉表
                crosstab = pd.crosstab(self.data[factor], self.data['stage'])
                crosstab_pct = pd.crosstab(self.data[factor], self.data['stage'], normalize='index') * 100
                
                # 堆叠条形图
                crosstab_pct.plot(kind='bar', stacked=True, ax=axes[i])
                axes[i].set_title(f'{factor} 与 CKD阶段关系')
                axes[i].set_xlabel(f'{factor} (0=无, 1=有)')
                axes[i].set_ylabel('百分比 (%)')
                axes[i].legend(title='CKD阶段')
                axes[i].tick_params(axis='x', rotation=0)
        
        # 删除多余的子图
        if len(risk_factors) < len(axes):
            for j in range(len(risk_factors), len(axes)):
                fig.delaxes(axes[j])
        
        plt.tight_layout()
        plt.show()
        
        # 风险因素组合分析
        self.data['total_risk_factors'] = (
            self.data['HBP'] + self.data['diabetes'] + 
            self.data['hyperuricemia'] + self.data['genetic'] + self.data['family']
        )
        
        plt.figure(figsize=(12, 8))
        risk_stage = pd.crosstab(self.data['total_risk_factors'], self.data['stage'])
        risk_stage_pct = pd.crosstab(self.data['total_risk_factors'], self.data['stage'], normalize='index') * 100
        
        risk_stage_pct.plot(kind='bar', width=0.8)
        plt.title('风险因素总数与CKD阶段关系')
        plt.xlabel('风险因素总数')
        plt.ylabel('百分比 (%)')
        plt.legend(title='CKD阶段')
        plt.xticks(rotation=0)
        plt.tight_layout()
        plt.show()
        
    def interactive_plots(self):
        """交互式图表"""
        print("生成交互式图表...")
        
        # 1. 3D散点图 - eGFR, Scr, ACR
        fig = px.scatter_3d(self.data, x='Scr', y='eGFR', z='ACR', 
                           color='stage', size='UP_index',
                           title='CKD患者关键指标3D分布',
                           labels={'Scr': '血肌酐', 'eGFR': 'eGFR', 'ACR': 'ACR'})
        fig.show()
        
        # 2. 平行坐标图
        # 选择数值型特征
        numeric_features = ['eGFR', 'Scr', 'ACR', 'UP_index', 'URC_HP']
        plot_data = self.data[numeric_features + ['stage']].copy()
        
        # 标准化数据以便可视化
        from sklearn.preprocessing import MinMaxScaler
        scaler = MinMaxScaler()
        plot_data[numeric_features] = scaler.fit_transform(plot_data[numeric_features])
        
        fig = px.parallel_coordinates(plot_data, color='stage',
                                    dimensions=numeric_features,
                                    title='CKD患者特征平行坐标图')
        fig.show()
        
        # 3. 热力图 - 医院vs CKD阶段
        hospital_stage = pd.crosstab(self.data['hos_name'], self.data['stage'])
        
        fig = px.imshow(hospital_stage.values,
                       x=[f'Stage {i}' for i in hospital_stage.columns],
                       y=[f'医院{i}' for i in hospital_stage.index],
                       title='各医院CKD阶段分布热力图',
                       color_continuous_scale='Blues')
        fig.show()
        
    def correlation_heatmap(self):
        """相关性热力图"""
        print("生成相关性热力图...")
        
        # 选择数值型特征
        numeric_cols = self.data.select_dtypes(include=[np.number]).columns
        correlation_matrix = self.data[numeric_cols].corr()
        
        # 创建掩码以隐藏上三角
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        
        plt.figure(figsize=(14, 12))
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', 
                   center=0, square=True, linewidths=0.5, cbar_kws={"shrink": .8})
        plt.title('特征相关性热力图')
        plt.tight_layout()
        plt.show()
        
        # 与目标变量的相关性
        target_corr = correlation_matrix[['stage', 'rate']].drop(['stage', 'rate'])
        
        fig, axes = plt.subplots(1, 2, figsize=(16, 8))
        
        # 与stage的相关性
        stage_corr = target_corr['stage'].sort_values(ascending=True)
        axes[0].barh(range(len(stage_corr)), stage_corr.values)
        axes[0].set_yticks(range(len(stage_corr)))
        axes[0].set_yticklabels(stage_corr.index)
        axes[0].set_title('特征与CKD阶段的相关性')
        axes[0].set_xlabel('相关系数')
        
        # 与rate的相关性
        rate_corr = target_corr['rate'].sort_values(ascending=True)
        axes[1].barh(range(len(rate_corr)), rate_corr.values)
        axes[1].set_yticks(range(len(rate_corr)))
        axes[1].set_yticklabels(rate_corr.index)
        axes[1].set_title('特征与CKD分层的相关性')
        axes[1].set_xlabel('相关系数')
        
        plt.tight_layout()
        plt.show()
        
    def medical_insights_visualization(self):
        """医学洞察可视化"""
        print("生成医学洞察可视化...")
        
        # 1. eGFR分布按CKD阶段
        plt.figure(figsize=(14, 8))
        
        # 小提琴图
        plt.subplot(2, 2, 1)
        sns.violinplot(data=self.data, x='stage', y='eGFR')
        plt.title('eGFR在不同CKD阶段的分布（小提琴图）')
        
        # 密度图
        plt.subplot(2, 2, 2)
        for stage in sorted(self.data['stage'].unique()):
            stage_data = self.data[self.data['stage'] == stage]['eGFR']
            sns.kdeplot(stage_data, label=f'Stage {stage}')
        plt.title('eGFR密度分布')
        plt.xlabel('eGFR')
        plt.legend()
        
        # 血肌酐分布
        plt.subplot(2, 2, 3)
        sns.violinplot(data=self.data, x='stage', y='Scr')
        plt.title('血肌酐在不同CKD阶段的分布')
        
        # ACR分布
        plt.subplot(2, 2, 4)
        sns.violinplot(data=self.data, x='stage', y='ACR')
        plt.title('ACR在不同CKD阶段的分布')
        
        plt.tight_layout()
        plt.show()
        
        # 2. 疾病进展分析
        # 创建疾病严重程度评分
        self.data['severity_score'] = (
            (5 - self.data['stage']) * 0.4 +  # CKD阶段权重
            (self.data['eGFR'] / 120) * 0.3 +  # eGFR权重
            (1 / (self.data['Scr'] / 100 + 1)) * 0.3  # 血肌酐权重
        )
        
        plt.figure(figsize=(12, 8))
        
        # 按性别分析
        plt.subplot(2, 2, 1)
        sns.boxplot(data=self.data, x='gender', y='severity_score')
        plt.title('疾病严重程度按性别分布')
        plt.xlabel('性别 (0=女性, 1=男性)')
        
        # 按年龄组分析（使用eGFR作为年龄代理）
        self.data['age_group'] = pd.cut(self.data['eGFR'], 
                                       bins=[0, 30, 60, 90, 120, float('inf')],
                                       labels=['很低', '低', '中等', '高', '很高'])
        
        plt.subplot(2, 2, 2)
        sns.boxplot(data=self.data, x='age_group', y='severity_score')
        plt.title('疾病严重程度按eGFR水平分布')
        plt.xticks(rotation=45)
        
        # 疾病史影响
        plt.subplot(2, 2, 3)
        disease_severity = self.data.groupby(['diabetes', 'HBP'])['severity_score'].mean().unstack()
        sns.heatmap(disease_severity, annot=True, cmap='RdYlBu_r')
        plt.title('糖尿病和高血压对疾病严重程度的影响')
        plt.xlabel('高血压 (0=无, 1=有)')
        plt.ylabel('糖尿病 (0=无, 1=有)')
        
        # 医院差异
        plt.subplot(2, 2, 4)
        hospital_severity = self.data.groupby('hos_name')['severity_score'].mean()
        hospital_severity.plot(kind='bar')
        plt.title('不同医院患者疾病严重程度')
        plt.xlabel('医院')
        plt.ylabel('平均严重程度评分')
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.show()

def main():
    """主函数"""
    print("慢性肾脏疾病（CKD）可视化分析")
    print("="*50)
    
    # 数据文件路径
    data_path = "数据挖掘实践期末考试材料/可参考选题1数据-慢性肾病状态预测/kidney_clean.csv"
    
    # 创建可视化分析实例
    viz_analyzer = CKDVisualization(data_path)
    
    # 执行可视化分析
    viz_analyzer.load_data()
    viz_analyzer.basic_distribution_plots()
    viz_analyzer.clinical_indicators_analysis()
    viz_analyzer.risk_factors_analysis()
    viz_analyzer.correlation_heatmap()
    viz_analyzer.medical_insights_visualization()
    
    # 交互式图表（需要plotly）
    try:
        viz_analyzer.interactive_plots()
    except Exception as e:
        print(f"交互式图表生成失败: {e}")
        print("请安装plotly: pip install plotly")
    
    print("\n可视化分析完成！")

if __name__ == "__main__":
    main()
